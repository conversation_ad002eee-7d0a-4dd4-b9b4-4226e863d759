"use client";

import React, { JSX, useState } from "react";
import Image from "next/image";
import ModernHeroWithBackground from '../../../components/ModernHeroWithBackground';
import { ContactPageConfig,FormErrors,FormData,ContactInfo} from "@/types/types";
// import {faqs } from "@/store";
import axios from "axios";


export default function ContactPage(): JSX.Element {
  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    lastName: '',
    email: '',
    company: '',
    message: ''
  });
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  // Configuration object for easy customization
  const config: ContactPageConfig = {
    hero: {
      title: <> Get in <span style={{color: '#F3C800'}}>Touch</span></>,
      description: <>{"Ready to bring the finest produce to your market? Let's create something amazing together."}</>,
      backgroundSrc: "/assets/photorealistic-woman-organic-sustainable.jpg",
      primaryButton: {
        text: "Our Products",
        href: "/products"
      },
      secondaryButton: {
        text: "Contact Us",
        href: "/contact"
      }
    },
    contactForm: {
      title: "Send Us a Message",
      subtitle: "Fill out the form and we'll get back to you within 24 hours.",
      responseTime: "24 hours"
    },
    contactInfo: {
      title: "Let's Connect",
      subtitle: "We're here to help you source the finest African produce for your market."
    },
    locationSection: {
      imageSrc: "/assets/landscape_view_of _palm_trees.jpg",
      imageAlt: "AfricSource location in Cote d'Ivoire",
      title: "Visit Our Facilities",
      description: "Experience the heart of African agriculture in beautiful Cote d'Ivoire"
    },
    faqSection: {
      title: "Frequently Asked Questions",
      subtitle: "Everything you need to know about working with AfricSource",
      ctaTitle: "Still Have Questions?",
      ctaDescription: "Our team is ready to help you find the perfect solution for your needs.",
      ctaButtonText: "Get In Touch"
    },
    colors: {
      primary: '#0C4B35',
      accent: '#F3C800',
      base: '#FFFFFF'
    }
  };


  // Contact information data
  const contactInfo: ContactInfo[] = [
    {
      type: 'location',
      title: 'Visit Us',
      primary: 'Bonoua, Cote d\'Ivoire.',
      secondary: 'Office 3016 182-184 High Street North, London, United Kingdom, E6 2JA, UK.',
      extra:"Nigerian Address and number; 21 ogunmade street, Ketu Lagos. I'll get the number soon.",
      icon: (
        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      )
    },
    {
      type: 'email',
      title: 'Email Us',
      primary: '<EMAIL>',
      secondary: 'Response within 24 hours',
      icon: (
        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
        </svg>
      )
    },
    {
      type: 'phone',
      title: 'Call Us',
      primary: '+44 7947362643',
      icon: (
        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
        </svg>
      )
    },
    {
      type: 'hours',
      title: 'Business Hours',
      primary: 'Monday - Friday: 8:00 AM - 6:00 PM (GMT)',
      secondary: 'Saturday: 10:00 AM - 4:00 PM (GMT)',
      extra: 'Sunday: Closed',
      icon: (
        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      )
    }
  ];

  // Form validation
  const validateForm = (): boolean => {
    const errors: FormErrors = {};
    
    if (!formData.firstName.trim()) {
      errors.firstName = 'First name is required';
    }
    
    if (!formData.lastName.trim()) {
      errors.lastName = 'Last name is required';
    }
    
    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Email is invalid';
    }
    
    if (!formData.message.trim()) {
      errors.message = 'Message is required';
    } else if (formData.message.trim().length < 10) {
      errors.message = 'Message must be at least 10 characters';
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form input changes
  const handleInputChange = (field: keyof FormData) => (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setFormData(prev => ({
      ...prev,
      [field]: e.target.value
    }));
    
    // Clear error when user starts typing
    if (formErrors[field]) {
      setFormErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }
  };

  // Handle form submission
 // Handle form submission
const handleSubmit = async (e: React.FormEvent) => {
  e.preventDefault();

  if (!validateForm()) {
    return;
  }

  setIsSubmitting(true);

  try {
    const response = await axios.post('/api/contact', formData);

    if (response.status === 200 || response.status === 201) {
      // Reset form on success
      setFormData({
        firstName: '',
        lastName: '',
        email: '',
        company: '',
        message: ''
      });

      alert('Message sent successfully! We\'ll get back to you within 24 hours.');
    } else {
      alert('Something went wrong. Please try again.');
    }
  } catch (err) {
    console.error("Contact form submission failed:", err);
    alert('Failed to send message. Please try again.');
  } finally {
    setIsSubmitting(false);
  }
};


  return (
    <div className="min-h-screen bg-white">
      <ModernHeroWithBackground
        title={config.hero.title}
        description={config.hero.description}
        backgroundSrc={config.hero.backgroundSrc}
        primaryButtonText={config.hero.primaryButton.text}
        primaryButtonHref={config.hero.primaryButton.href}
        secondaryButtonText={config.hero.secondaryButton.text}
        secondaryButtonHref={config.hero.secondaryButton.href}
      />

      {/* Contact Form Section with Light Background */}
      <section id="contact-form" className="py-20 bg-gray-50">
        <div className="container mx-auto px-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-start">
            {/* Contact Form */}
            <div className="bg-white rounded-3xl p-8 shadow-2xl">
              <h2 className="text-3xl font-bold text-gray-800 mb-2">{config.contactForm.title}</h2>
              <p className="text-gray-600 mb-8">{config.contactForm.subtitle}</p>
              
              <form className="space-y-6" onSubmit={handleSubmit}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      First Name *
                    </label>
                    <input 
                      type="text" 
                      value={formData.firstName}
                      onChange={handleInputChange('firstName')}
                      className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:border-transparent transition-all duration-200 ${
                        formErrors.firstName ? 'border-red-300' : 'border-gray-300'
                      }`}
                      style={{'--tw-ring-color': config.colors.primary} as React.CSSProperties & Record<string, string>}
                      placeholder="Your first name"
                    />
                    {formErrors.firstName && (
                      <p className="text-red-500 text-sm mt-1">{formErrors.firstName}</p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Last Name *
                    </label>
                    <input 
                      type="text" 
                      value={formData.lastName}
                      onChange={handleInputChange('lastName')}
                      className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:border-transparent transition-all duration-200 ${
                        formErrors.lastName ? 'border-red-300' : 'border-gray-300'
                      }`}
                      style={{'--tw-ring-color': config.colors.primary} as React.CSSProperties & Record<string, string>}
                      placeholder="Your last name"
                    />
                    {formErrors.lastName && (
                      <p className="text-red-500 text-sm mt-1">{formErrors.lastName}</p>
                    )}
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email Address *
                  </label>
                  <input 
                    type="email" 
                    value={formData.email}
                    onChange={handleInputChange('email')}
                    className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:border-transparent transition-all duration-200 ${
                      formErrors.email ? 'border-red-300' : 'border-gray-300'
                    }`}
                    style={{'--tw-ring-color': config.colors.primary} as React.CSSProperties & Record<string, string>}
                    placeholder="<EMAIL>"
                  />
                  {formErrors.email && (
                    <p className="text-red-500 text-sm mt-1">{formErrors.email}</p>
                  )}
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Company</label>
                  <input 
                    type="text" 
                    value={formData.company}
                    onChange={handleInputChange('company')}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:border-transparent transition-all duration-200"
                    style={{'--tw-ring-color': config.colors.primary} as React.CSSProperties & Record<string, string>}
                    placeholder="Your company name"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Message *
                  </label>
                  <textarea 
                    rows={6}
                    value={formData.message}
                    onChange={handleInputChange('message')}
                    className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:border-transparent transition-all duration-200 resize-none ${
                      formErrors.message ? 'border-red-300' : 'border-gray-300'
                    }`}
                    style={{'--tw-ring-color': config.colors.primary} as React.CSSProperties & Record<string, string>}
                    placeholder="Tell us about your requirements..."
                  ></textarea>
                  {formErrors.message && (
                    <p className="text-red-500 text-sm mt-1">{formErrors.message}</p>
                  )}
                </div>
                
                <button 
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full text-white py-4 rounded-xl font-bold text-lg transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                  style={{backgroundColor: config.colors.primary}}
                  onMouseEnter={(e) => !isSubmitting && ((e.target as HTMLButtonElement).style.backgroundColor = '#094030')}
                  onMouseLeave={(e) => !isSubmitting && ((e.target as HTMLButtonElement).style.backgroundColor = config.colors.primary)}
                >
                  {isSubmitting ? 'Sending...' : 'Send Message'}
                </button>
              </form>
            </div>

            {/* Contact Information */}
            <div className="text-gray-700 space-y-8">
              <div>
                <h2 className="text-4xl font-bold mb-4" style={{color: config.colors.primary}}>
                  {config.contactInfo.title}
                </h2>
                <p className="text-xl text-gray-600 mb-8">
                  {config.contactInfo.subtitle}
                </p>
              </div>

              <div className="space-y-6">
                {contactInfo.map((info) => (
                  <div key={info.type} className="flex items-start space-x-4 bg-white rounded-2xl p-6 shadow-md hover:shadow-lg transition-all duration-300">
                    <div className="w-12 h-12 rounded-full flex items-center justify-center flex-shrink-0" style={{backgroundColor: config.colors.accent}}>
                      {info.icon}
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold mb-1" style={{color: config.colors.primary}}>
                        {info.title}
                      </h3>
                      {info.type === 'email' ? (
                        <a 
                          href={`mailto:${info.primary}`} 
                          className="text-lg hover:opacity-75 transition-colors duration-200" 
                          style={{color: config.colors.primary}}
                        >
                          {info.primary}
                        </a>
                      ) : info.type === 'phone' ? (
                        <div className="space-y-1">
                          <p className="text-lg text-gray-700">{info.primary}</p>
                          <p className="text-lg flex items-center gap-2 text-gray-700">
                            {info.secondary}
                          </p>
                        </div>
                      ) : info.type === 'hours' ? (
                        <div className="space-y-1 text-sm text-gray-600">
                          <p>{info.primary}</p>
                          <p>{info.secondary}</p>
                          <p className="text-gray-500">{info.extra}</p>
                        </div>
                      ) : (
                        <>
                          <p className="text-lg text-gray-700">{info.primary}</p>
                          <p className="text-sm text-gray-700">{info.secondary}</p>
                          <p className="text-sm text-gray-700">{info.extra}</p>
                        </>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Location Image Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-6">
          <div className="rounded-3xl overflow-hidden shadow-2xl h-96 relative">
            <Image
              src={config.locationSection.imageSrc}
              alt={config.locationSection.imageAlt}
              fill
              className="object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
            <div className="absolute bottom-0 left-0 right-0 p-8 text-white">
              <div className="max-w-md">
                <h3 className="text-3xl font-bold mb-2">{config.locationSection.title}</h3>
                <p className="text-lg opacity-90">{config.locationSection.description}</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section with Deep Green Background */}
    
    </div>
  );
}