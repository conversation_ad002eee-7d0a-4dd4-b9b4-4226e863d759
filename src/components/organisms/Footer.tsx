import Link from "next/link";
import { getSocialLinksWithFallback } from "@/lib/social-config";
import Image from "next/image";

export default function Footer() {
  const currentYear = new Date().getFullYear();
  const socialLinks = getSocialLinksWithFallback();

  return (
    <footer className="bg-[#0C4B35] text-white py-16 px-6 md:px-24 mt-11">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-12">
        {/* Brand Column */}
        <div className="space-y-6">
          <div className="flex items-center space-x-3">
            <Image
              src="/assets/White.png"
              alt="AfricSource Logo"
              width={200}
              height={100}
              className="object-cover transition-transform duration-500 group-hover:scale-110"
              onError={(e) => {
                e.currentTarget.src = "/globe.svg";
              }}
            />
          </div>

          <p className="text-white/70 text-sm max-w-xs leading-relaxed">
            {"Connecting Africa's premium commodities to global markets through sustainable export solutions, quality assurance, and international trade expertise."}
          </p>

          {/* Social Media Links */}
          <div className="flex space-x-3">
            {socialLinks.map((social) => {
              const IconComponent = social.icon;
              return (
                <a
                  key={social.name}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 rounded-lg bg-white/10 backdrop-blur-sm border border-white/20 flex items-center justify-center hover:bg-[#F3C800] hover:text-[#0C4B35] transition-all duration-300"
                  aria-label={`Follow us on ${social.name}`}
                >
                  <span className="sr-only">Follow us on {social.name}</span>
                  <IconComponent className="w-5 h-5" />
                </a>
              );
            })}
          </div>
        </div>

        {/* Export Services */}
        <div>
          <h3 className="text-lg font-semibold mb-6 text-[#F3C800]">
            Export Services
          </h3>
          {/* name: "Home",
                href: "/",
              },
              {
                name: "About",
                href: "/about",
              },
              {
                name: "Products",
                href: "/products",
              },
              {
                name: "Sustainability",
                href: "/sustainability",
              },
              {
                name: "Contact",
                href: "/contact", */}
          <ul className="space-y-4">
            {[
              { name: "Premium Products", href: "/products" },
              { name: "About", href: "/about" },
              { name: "Contact", href: "/contact" },
              { name: "Sustainability", href: "/sustainability" },
            ].map((link) => (
              <li key={link.name}>
                <Link
                  href={link.href}
                  className="text-white/70 hover:text-[#F3C800] transition-colors duration-300 flex items-center group"
                >
                  <svg
                    className="w-3 h-3 mr-2 opacity-0 group-hover:opacity-100 transition-opacity"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                  {link.name}
                </Link>
              </li>
            ))}
          </ul>
        </div>

        {/* Contact Information */}
        <div>
          <h3 className="text-lg font-semibold mb-6 text-[#F3C800]">
            Global Offices
          </h3>
          <div className="space-y-6">
            {/* Main Office */}
            <div>
              <h4 className="text-white font-medium mb-3">Head Office</h4>
              <ul className="space-y-3 text-white/70 text-sm">
                <li className="flex items-start space-x-3">
                  <div className="w-4 h-4 mt-0.5 bg-[#F3C800] rounded-sm flex-shrink-0"></div>
                  <span>{"Bonoua, Côte d'Ivoire"}</span>
                </li>
                <li className="flex items-start space-x-3">
                  <div className="w-4 h-4 mt-0.5 bg-[#F3C800] rounded-sm flex-shrink-0"></div>
                  <span>Office 3016 182-184 High Street North, London, United Kingdom, E6 2JA</span>
                </li>
                <li className="flex items-start space-x-3">
                  <div className="w-4 h-4 mt-0.5 bg-[#F3C800] rounded-sm flex-shrink-0"></div>
                  <span><EMAIL></span>
                </li>
              </ul>
            </div>

            {/* Contact Numbers */}
            <div>
              <h4 className="text-white font-medium mb-3">Export Inquiries</h4>
              <ul className="space-y-3 text-white/70 text-sm">
                <li className="flex items-start space-x-3">
                  <div className="w-4 h-4 mt-0.5 bg-[#F3C800] rounded-sm flex-shrink-0"></div>
                  <span>+44 7947362643</span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Export Newsletter */}
      </div>

      {/* Footer Bottom / Copyright */}
      <div className="border-t border-white/10 mt-12 pt-8">
        <div className="flex flex-col md:flex-row justify-between items-center gap-6">
          <div className="flex flex-col md:flex-row items-center gap-4">
            <p className="text-white/60 text-sm">
              &copy; {currentYear} AfricSource. All rights reserved.
            </p>
            <div className="flex items-center gap-2 text-white/60 text-sm">
              <span>🌍</span>
              <span>Connecting Africa to Global Markets</span>
            </div>
          </div>

          {/* <div className="flex flex-wrap gap-6">
            <Link href="/privacy-policy" className="text-white/60 hover:text-[#F3C800] text-sm transition-colors duration-300">
              Privacy Policy
            </Link>
            <Link href="/terms-of-service" className="text-white/60 hover:text-[#F3C800] text-sm transition-colors duration-300">
              Terms of Service
            </Link>
            <Link href="/export-terms" className="text-white/60 hover:text-[#F3C800] text-sm transition-colors duration-300">
              Export Terms
            </Link>
          </div> */}
        </div>
      </div>
    </footer>
  );
}
