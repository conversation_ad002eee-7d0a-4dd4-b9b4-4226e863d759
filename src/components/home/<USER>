import React from 'react';
import Image from 'next/image';
import { highlights } from '@/store'; // Assuming highlights is an array of objects

export default function CompanyHighlights() {
  return (
    // Main section with extended height to accommodate airplane image
    // - Keep 'relative' and 'w-full'
    // - Remove 'overflow-hidden' to allow the airplane image to extend above
    // - Add custom wave class and negative margin to allow airplane to poke out
    <section className="relative w-full section-wave-bottom-highlights -mt-16 pt-16">
      {/* Background Image Container - Extended to show full airplane */}
      <div className="absolute inset-0 -top-16">
        <Image
          src="/assets/global-shipping-logistics.png"
          alt="Global Shipping Logistics Background with Airplane"
          fill
          className="object-cover object-top"
          priority
          sizes="100vw"
          style={{
            objectPosition: 'center top',
            transform: 'translateY(-10px)' // Slight upward adjustment to show more airplane
          }}
        />

        {/* Gradient Overlay - Adjusted to work with extended background */}
        <div className="absolute inset-0 bg-gradient-to-b
                        from-transparent via-black/30 to-black/80
                        sm:from-transparent sm:via-black/40 sm:to-black/90
                        lg:from-transparent lg:via-black/50 lg:to-black"
        />
      </div>

      {/* Content */}
      {/*
        The padding-top (pt-*) handles pushing the content down,
        creating space above it for the airplane image to be fully visible.
        Increased padding to accommodate the extended background.
      */}
      <div className="relative z-10 max-w-7xl mx-auto
                      grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4
                      gap-10 px-4
                      pt-24 sm:pt-32 lg:pt-56 pb-8 sm:pb-12 lg:pb-16">
        {highlights.map(({ title, description, icon: Icon }) => (
          <div key={title} className="flex flex-col items-center text-center gap-3">
            {/* Icon */}
            <div className="text-sun-gold">
              <Icon size={66} />
            </div>

            {/* Text */}
            <div>
              <h5 className="text-[18px] font-baru-semibold text-white">
                {title}
              </h5>
              <p className="text-[10px] font-baru-regular text-harvest-green leading-snug">
                {description}
              </p>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
}