import React from 'react';
import Image from 'next/image';
import { highlights } from '@/store'; // Assuming highlights is an array of objects

export default function CompanyHighlights() {
  return (
    // Main section:
    // - Keep 'relative' and 'w-full'.
    // - REMOVE 'overflow-hidden' to allow the image to poke out.
    // - Add our custom wave class 'section-wave-bottom-highlights'.
    <section className="relative w-full section-wave-bottom-highlights">
      {/* Background Image Container */}
      <div className="absolute inset-0">
        <Image
          src="/assets/global-shipping-logistics.png"
          alt="Global Shipping Logistics Background with Airplane"
          fill
          className="object-cover object-top"
          priority
          sizes="100vw"
        />

        {/* Gradient Overlay - Now covers the entire section */}
        <div className="absolute inset-0 bg-gradient-to-b
                        from-transparent via-black/40 to-black/80
                        sm:from-transparent sm:via-black/50 sm:to-black/90
                        lg:from-transparent lg:via-black/60 lg:to-black"
        />
      </div>

      {/* Content */}
      {/*
        The padding-top (pt-*) handles pushing the content down,
        creating space above it for the image to "pop out" into.
      */}
      <div className="relative z-10 max-w-7xl mx-auto
                      grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4
                      gap-10 px-4
                      pt-20 sm:pt-28 lg:pt-48 pb-8 sm:pb-12 lg:pb-16">
        {highlights.map(({ title, description, icon: Icon }) => (
          <div key={title} className="flex flex-col items-center text-center gap-3">
            {/* Icon */}
            <div className="text-sun-gold">
              <Icon size={66} />
            </div>

            {/* Text */}
            <div>
              <h5 className="text-[18px] font-baru-semibold text-white">
                {title}
              </h5>
              <p className="text-[10px] font-baru-regular text-harvest-green leading-snug">
                {description}
              </p>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
}